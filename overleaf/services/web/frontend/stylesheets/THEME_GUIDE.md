# Enterprise SaaS Theme Documentation

## Overview

This document outlines the modern enterprise SaaS color scheme implemented for Overleaf, designed specifically for document processing applications with enterprise clients in mind.

## Color Philosophy

### Design Principles
- **Professional**: Sophisticated colors that convey trust and reliability
- **Accessible**: WCAG 2.1 AA compliant contrast ratios
- **Eye-friendly**: Warm grays to reduce eye strain during long reading/writing sessions
- **Systematic**: Numbered color scales (10-70) for consistent hierarchy
- **Flexible**: Easy customization for different enterprise brands

## Color Palette

### Neutral Colors (Warm Grays)
Perfect for text, backgrounds, and borders. Optimized to reduce eye fatigue.

```scss
$neutral-10: #fafaf9   // Very light - subtle backgrounds
$neutral-20: #f5f5f4   // Light - card backgrounds
$neutral-30: #e7e5e4   // Medium light - borders
$neutral-40: #d6d3d1   // Medium - disabled states
$neutral-50: #a8a29e   // Medium gray - placeholders
$neutral-60: #78716c   // Medium dark - secondary text
$neutral-70: #57534e   // Dark - primary text
$neutral-80: #3c3835   // Very dark - dark backgrounds
$neutral-90: #1c1917   // Almost black - high contrast text
```

### Professional Blue (Primary Brand)
Deep, trustworthy blues perfect for enterprise applications.

```scss
$blue-10: #eff6ff     // Info backgrounds
$blue-20: #dbeafe     // Hover states
$blue-30: #93c5fd     // Accent elements
$blue-40: #60a5fa     // Interactive elements
$blue-50: #3b82f6     // Primary brand color
$blue-60: #2563eb     // Hover states
$blue-70: #1d4ed8     // Pressed states
```

### Forest Green (Success States)
Professional green for positive actions and success states.

```scss
$green-50: #059669    // Success primary
$green-60: #047857    // Success hover
$green-70: #065f46    // Success pressed
```

### Additional Colors
- **Red**: Professional crimson for errors and critical actions
- **Amber**: Refined orange for warnings and attention states

## Semantic Color Usage

### Backgrounds
- `$bg-light-primary`: Main background for light theme
- `$bg-light-secondary`: Card and panel backgrounds
- `$bg-light-tertiary`: Subtle section backgrounds

### Text
- `$content-primary`: Primary text color
- `$content-secondary`: Secondary text, labels
- `$content-disabled`: Disabled text states

### Interactive Elements
- `$link-ui`: Primary link color
- `$link-ui-hover`: Link hover state
- `$border-active`: Active input borders

## Brand Customization

### For Enterprise Clients
Customize your brand colors by modifying `/abstracts/brand-config.scss`:

```scss
// Example: Corporate Blue Theme
$brand-primary: #1e3a8a;           // Deep navy
$brand-success: #166534;           // Forest green

// Example: Modern Tech Theme
$brand-primary: #7c3aed;           // Purple
$brand-success: #059669;           // Emerald
```

### CSS Custom Properties
For runtime theme switching, use CSS custom properties:

```css
:root {
  --brand-primary: #3b82f6;
  --brand-success: #059669;
}

/* Dark theme overrides */
[data-theme="dark"] {
  --brand-primary: #60a5fa;
}
```

## Implementation Steps

1. **Backup Current Theme**: Always backup before making changes
2. **Update Color Foundation**: Modify `colors.scss` with new palette
3. **Configure Brand Colors**: Customize `brand-config.scss`
4. **Test Compilation**: Ensure SCSS compiles without errors
5. **Verify Accessibility**: Check contrast ratios meet WCAG standards
6. **Test UI Components**: Verify all components render correctly

## Accessibility

All color combinations meet WCAG 2.1 AA standards:
- Primary text on light backgrounds: 7:1+ contrast ratio
- Secondary text on light backgrounds: 4.5:1+ contrast ratio
- Interactive elements have clear focus states
- Color is never the only way to convey information

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Development Workflow

### Building Styles
```bash
# Compile SCSS to CSS
npm run build-css

# Watch for changes during development
npm run watch-css
```

### Testing Changes
1. Start the development server
2. Navigate to different pages to test color consistency
3. Test both light and dark themes
4. Verify mobile responsiveness
5. Check accessibility with browser tools

## Troubleshooting

### Common Issues
- **Colors not updating**: Clear browser cache and rebuild CSS
- **Contrast warnings**: Adjust color values in `brand-config.scss`
- **Build errors**: Check SCSS syntax and variable references

### Support
For questions about theme customization, refer to:
- This documentation
- Bootstrap 5 color system documentation
- WCAG accessibility guidelines

---

*This theme system is designed to grow with your enterprise needs while maintaining consistency and accessibility.*
