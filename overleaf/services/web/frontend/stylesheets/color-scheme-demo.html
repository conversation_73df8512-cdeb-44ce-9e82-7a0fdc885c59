<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise SaaS Color Scheme - Overleaf</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            background: #fafaf9;
            color: #57534e;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: #3b82f6;
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .color-group {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .color-group h3 {
            margin-bottom: 1rem;
            color: #1c1917;
            font-size: 1.2rem;
        }

        .color-swatch {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            border-radius: 4px;
            border: 1px solid #e7e5e4;
        }

        .color-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 1rem;
            border: 2px solid #e7e5e4;
        }

        .color-info {
            flex: 1;
        }

        .color-name {
            font-weight: 600;
            color: #1c1917;
            font-size: 0.9rem;
        }

        .color-value {
            font-family: 'Courier New', monospace;
            color: #78716c;
            font-size: 0.8rem;
        }

        .buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-success {
            background: #059669;
            color: white;
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-warning {
            background: #d97706;
            color: white;
        }

        .btn-warning:hover {
            background: #b45309;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .btn-secondary {
            background: #f5f5f4;
            color: #57534e;
            border: 1px solid #e7e5e4;
        }

        .btn-secondary:hover {
            background: #e7e5e4;
        }

        .components-demo {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #1c1917;
        }

        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e7e5e4;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.2s;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .alert {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #f0fdf4;
            color: #065f46;
            border: 1px solid #86efac;
        }

        .alert-warning {
            background: #fffbeb;
            color: #92400e;
            border: 1px solid #fcd34d;
        }

        .alert-danger {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .alert-info {
            background: #eff6ff;
            color: #1d4ed8;
            border: 1px solid #93c5fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Enterprise SaaS Color Scheme</h1>
            <p>Modern, professional color palette designed for document processing applications</p>
        </div>

        <div class="grid">
            <div class="color-group">
                <h3>Neutral Colors</h3>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #fafaf9;"></div>
                    <div class="color-info">
                        <div class="color-name">Neutral 10</div>
                        <div class="color-value">#fafaf9</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #f5f5f4;"></div>
                    <div class="color-info">
                        <div class="color-name">Neutral 20</div>
                        <div class="color-value">#f5f5f4</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #a8a29e;"></div>
                    <div class="color-info">
                        <div class="color-name">Neutral 50</div>
                        <div class="color-value">#a8a29e</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #57534e;"></div>
                    <div class="color-info">
                        <div class="color-name">Neutral 70</div>
                        <div class="color-value">#57534e</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #1c1917;"></div>
                    <div class="color-info">
                        <div class="color-name">Neutral 90</div>
                        <div class="color-value">#1c1917</div>
                    </div>
                </div>
            </div>

            <div class="color-group">
                <h3>Professional Blue</h3>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #eff6ff;"></div>
                    <div class="color-info">
                        <div class="color-name">Blue 10</div>
                        <div class="color-value">#eff6ff</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #93c5fd;"></div>
                    <div class="color-info">
                        <div class="color-name">Blue 30</div>
                        <div class="color-value">#93c5fd</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #3b82f6;"></div>
                    <div class="color-info">
                        <div class="color-name">Blue 50 (Primary)</div>
                        <div class="color-value">#3b82f6</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #2563eb;"></div>
                    <div class="color-info">
                        <div class="color-name">Blue 60</div>
                        <div class="color-value">#2563eb</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #1d4ed8;"></div>
                    <div class="color-info">
                        <div class="color-name">Blue 70</div>
                        <div class="color-value">#1d4ed8</div>
                    </div>
                </div>
            </div>

            <div class="color-group">
                <h3>Forest Green</h3>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #f0fdf4;"></div>
                    <div class="color-info">
                        <div class="color-name">Green 10</div>
                        <div class="color-value">#f0fdf4</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #86efac;"></div>
                    <div class="color-info">
                        <div class="color-name">Green 30</div>
                        <div class="color-value">#86efac</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #059669;"></div>
                    <div class="color-info">
                        <div class="color-name">Green 50 (Success)</div>
                        <div class="color-value">#059669</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #047857;"></div>
                    <div class="color-info">
                        <div class="color-name">Green 60</div>
                        <div class="color-value">#047857</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #065f46;"></div>
                    <div class="color-info">
                        <div class="color-name">Green 70</div>
                        <div class="color-value">#065f46</div>
                    </div>
                </div>
            </div>

            <div class="color-group">
                <h3>Alert Colors</h3>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #dc2626;"></div>
                    <div class="color-info">
                        <div class="color-name">Red 50 (Error)</div>
                        <div class="color-value">#dc2626</div>
                    </div>
                </div>
                <div class="color-swatch">
                    <div class="color-circle" style="background: #d97706;"></div>
                    <div class="color-info">
                        <div class="color-name">Amber 50 (Warning)</div>
                        <div class="color-value">#d97706</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="components-demo">
            <h2 style="margin-bottom: 2rem; color: #1c1917;">UI Components Preview</h2>

            <div class="buttons">
                <button class="btn btn-primary">Primary Action</button>
                <button class="btn btn-success">Success Action</button>
                <button class="btn btn-warning">Warning Action</button>
                <button class="btn btn-danger">Delete Action</button>
                <button class="btn btn-secondary">Secondary Action</button>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                <div>
                    <h3 style="margin-bottom: 1rem; color: #1c1917;">Form Elements</h3>
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="email" id="email" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="role">Role</label>
                        <select id="role">
                            <option>Select a role</option>
                            <option>Administrator</option>
                            <option>Editor</option>
                            <option>Viewer</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea id="notes" rows="3" placeholder="Add any additional notes..."></textarea>
                    </div>
                </div>

                <div>
                    <h3 style="margin-bottom: 1rem; color: #1c1917;">Alert Messages</h3>
                    <div class="alert alert-success">
                        <strong>Success!</strong> Your document has been saved successfully.
                    </div>
                    <div class="alert alert-info">
                        <strong>Info:</strong> New collaboration features are now available.
                    </div>
                    <div class="alert alert-warning">
                        <strong>Warning:</strong> Your document has unsaved changes.
                    </div>
                    <div class="alert alert-danger">
                        <strong>Error:</strong> Failed to compile document. Please check syntax.
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top: 3rem; padding: 2rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h2 style="margin-bottom: 1rem; color: #1c1917;">Design Principles</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                <div>
                    <h4 style="color: #3b82f6; margin-bottom: 0.5rem;">Professional</h4>
                    <p style="color: #78716c;">Sophisticated colors that convey trust and reliability for enterprise users.</p>
                </div>
                <div>
                    <h4 style="color: #059669; margin-bottom: 0.5rem;">Accessible</h4>
                    <p style="color: #78716c;">WCAG 2.1 AA compliant contrast ratios ensure readability for all users.</p>
                </div>
                <div>
                    <h4 style="color: #d97706; margin-bottom: 0.5rem;">Eye-friendly</h4>
                    <p style="color: #78716c;">Warm grays reduce eye strain during long document editing sessions.</p>
                </div>
                <div>
                    <h4 style="color: #dc2626; margin-bottom: 0.5rem;">Systematic</h4>
                    <p style="color: #78716c;">Numbered color scales provide consistent hierarchy and flexibility.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
