/* ====== Enterprise Brand Configuration ====== */

/*
 * This file contains brand-specific color overrides that can be easily
 * customized for different enterprise clients or white-label deployments.
 */

/* ====== Primary Brand Colors ====== */
// Override these variables to customize the primary brand colors
$brand-primary: $blue-50 !default; // Main brand color
$brand-primary-hover: $blue-60 !default; // Brand hover state
$brand-primary-active: $blue-70 !default; // Brand active/pressed state
$brand-primary-light: $blue-10 !default; // Light brand backgrounds

/* ====== Secondary Brand Colors ====== */
$brand-secondary: $neutral-70 !default; // Secondary brand color
$brand-secondary-hover: $neutral-80 !default; // Secondary hover state

/* ====== Logo and Header Customization ====== */
// These can be overridden to match your organization's brand
$header-brand-bg: $brand-primary !default;
$header-text-color: $white !default;
$header-logo-filter: none !default; // CSS filter for logo customization

/* ====== Success/Action Colors ====== */
$brand-success: $green-50 !default; // Success actions (save, complete)
$brand-success-hover: $green-60 !default;

/* ====== Interactive Elements ====== */
$link-primary: $brand-primary !default; // Primary links
$link-primary-hover: $brand-primary-hover !default;
$button-primary-bg: $brand-primary !default;
$button-primary-text: $white !default;

/* ====== Custom CSS Properties for Runtime Theme Switching ====== */
:root {
  --brand-primary: #{$brand-primary};
  --brand-primary-hover: #{$brand-primary-hover};
  --brand-primary-active: #{$brand-primary-active};
  --brand-primary-light: #{$brand-primary-light};
  --brand-secondary: #{$brand-secondary};
  --brand-success: #{$brand-success};
  --header-brand-bg: #{$header-brand-bg};
  --header-text-color: #{$header-text-color};
}

/* ====== Dark Theme Overrides ====== */
[data-theme='dark'] {
  --brand-primary: #{$blue-40}; // Lighter blue for dark backgrounds
  --brand-primary-hover: #{$blue-30};
  --brand-primary-light: #{$blue-70}; // Darker blue for dark theme backgrounds
}

/* ====== Enterprise Theme Examples ====== */

/*
 * Uncomment and customize these examples for specific enterprise clients:
 */

/* Example: Conservative Corporate Theme */

/*
$brand-primary: #1e3a8a !default;           // Deep navy blue
$brand-success: #166534 !default;           // Forest green
*/

/* Example: Modern Tech Theme */

/*
$brand-primary: #7c3aed !default;           // Purple
$brand-success: #059669 !default;           // Emerald
*/

/* Example: Financial Services Theme */

/*
$brand-primary: #1e40af !default;           // Professional blue
$brand-secondary: #374151 !default;         // Charcoal gray
*/
