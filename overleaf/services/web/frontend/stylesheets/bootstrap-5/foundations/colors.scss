// This file provides CSS and Sass variables for colors as full color values.
// To combine one of these colors with an alpha value, use Sass's built-in
// rgba() function.

// Note that colors used by Bootstrap's Sass are used in calculations and
// therefore cannot contain CSS variables

/* ====== Modern Enterprise SaaS Color Palette ====== */

/* Neutral - Warm grays optimized for document processing */
$white: #fff;
$neutral-10: #fafaf9; // Very light warm gray - backgrounds
$neutral-20: #f5f5f4; // Light warm gray - subtle backgrounds
$neutral-30: #e7e5e4; // Light gray - borders, dividers
$neutral-40: #d6d3d1; // Medium light gray - disabled states
$neutral-50: #a8a29e; // Medium gray - placeholders
$neutral-60: #78716c; // Medium dark gray - secondary text
$neutral-70: #57534e; // Dark gray - primary text
$neutral-80: #3c3835; // Very dark gray - dark theme backgrounds
$neutral-90: #1c1917; // Almost black - primary dark text

/* Professional Blue - Primary brand and UI elements */
$blue-10: #eff6ff; // Very light blue - info backgrounds
$blue-20: #dbeafe; // Light blue - hover states
$blue-30: #93c5fd; // Medium light blue - accent elements
$blue-40: #60a5fa; // Medium blue - interactive elements
$blue-50: #3b82f6; // Primary blue - main brand color
$blue-60: #2563eb; // Darker blue - hover states
$blue-70: #1d4ed8; // Deep blue - pressed states

/* Forest Green - Success and positive actions */
$green-10: #f0fdf4; // Very light green - success backgrounds
$green-20: #dcfce7; // Light green - success hover states
$green-30: #86efac; // Medium light green - success accents
$green-40: #4ade80; // Medium green - success interactive
$green-50: #059669; // Professional green - success primary
$green-60: #047857; // Darker green - success hover
$green-70: #065f46; // Deep green - success pressed

/* Sophisticated Red - Errors and critical actions */
$red-10: #fef2f2; // Very light red - error backgrounds
$red-20: #fecaca; // Light red - error hover states
$red-30: #fca5a5; // Medium light red - error accents
$red-40: #f87171; // Medium red - error interactive
$red-50: #dc2626; // Professional red - error primary
$red-60: #b91c1c; // Darker red - error hover
$red-70: #991b1b; // Deep red - error pressed

/* Refined Amber - Warnings and attention */
$yellow-10: #fffbeb; // Very light amber - warning backgrounds
$yellow-20: #fef3c7; // Light amber - warning hover states
$yellow-30: #fcd34d; // Medium light amber - warning accents
$yellow-40: #f59e0b; // Medium amber - warning interactive
$yellow-50: #d97706; // Professional amber - warning primary
$yellow-60: #b45309; // Darker amber - warning hover
$yellow-70: #92400e; // Deep amber - warning pressed

/* ====== Semantic Sass color variables ====== */
$bg-light-primary: $white;
$bg-light-secondary: $neutral-10;
$bg-light-tertiary: $neutral-20;
$bg-light-disabled: $neutral-20;
$bg-dark-primary: $neutral-90;
$bg-dark-secondary: $neutral-80;
$bg-dark-tertiary: $neutral-70;
$bg-dark-disabled: $neutral-70;
$bg-accent-01: $green-50;
$bg-accent-02: $green-60;
$bg-accent-03: $green-10;
$bg-danger-01: $red-50;
$bg-danger-02: $red-60;
$bg-danger-03: $red-10;
$bg-warning-01: $yellow-50;
$bg-warning-02: $yellow-60;
$bg-warning-03: $yellow-10;
$bg-info-01: $blue-50;
$bg-info-02: $blue-60;
$bg-info-03: $blue-10;
$content-primary: $neutral-90;
$content-secondary: $neutral-70;
$content-disabled: $neutral-40;
$content-placeholder: $neutral-60;
$content-danger: $red-50;
$content-warning: $yellow-50;
$content-positive: $green-50;
$content-info: $blue-50;
$border-primary: $neutral-60;
$border-hover: $neutral-70;
$border-disabled: $neutral-20;
$border-active: $blue-50;
$border-danger: $red-50;
$border-divider: $neutral-20;
$link-web: $green-60;
$link-web-hover: $green-70;
$link-web-visited: $green-60;
$link-ui: $blue-50;
$link-ui-hover: $blue-60;
$link-ui-visited: $blue-60;
$content-primary-dark: $white;
$content-secondary-dark: $neutral-20;
$content-disabled-dark: $neutral-60;
$content-placeholder-dark: $neutral-50;
$content-danger-dark: $red-40;
$content-warning-dark: $yellow-40;
$content-positive-dark: $green-40;
$content-info-dark: $blue-30;
$border-primary-dark: $neutral-30;
$border-hover-dark: $neutral-20;
$border-disabled-dark: $neutral-80;
$border-active-dark: $blue-30;
$border-danger-dark: $red-40;
$border-divider-dark: $neutral-80;
$link-web-dark: $green-30;
$link-web-hover-dark: $green-40;
$link-web-visited-dark: $green-40;
$link-ui-dark: $blue-30;
$link-ui-hover-dark: $blue-40;
$link-ui-visited-dark: $blue-40;

/* ====== CSS color variables ====== */
:root {
  /* Neutral */
  --white: #{$white};
  --neutral-10: #{$neutral-10};
  --neutral-20: #{$neutral-20};
  --neutral-30: #{$neutral-30};
  --neutral-40: #{$neutral-40};
  --neutral-50: #{$neutral-50};
  --neutral-60: #{$neutral-60};
  --neutral-70: #{$neutral-70};
  --neutral-80: #{$neutral-80};
  --neutral-90: #{$neutral-90};

  /* Green */
  --green-10: #{$green-10};
  --green-20: #{$green-20};
  --green-30: #{$green-30};
  --green-40: #{$green-40};
  --green-50: #{$green-50};
  --green-60: #{$green-60};
  --green-70: #{$green-70};

  /* Blue */
  --blue-10: #{$blue-10};
  --blue-20: #{$blue-20};
  --blue-30: #{$blue-30};
  --blue-40: #{$blue-40};
  --blue-50: #{$blue-50};
  --blue-60: #{$blue-60};
  --blue-70: #{$blue-70};

  /* Red */
  --red-10: #{$red-10};
  --red-20: #{$red-20};
  --red-30: #{$red-30};
  --red-40: #{$red-40};
  --red-50: #{$red-50};
  --red-60: #{$red-60};
  --red-70: #{$red-70};

  /* Yellow */
  --yellow-10: #{$yellow-10};
  --yellow-20: #{$yellow-20};
  --yellow-30: #{$yellow-30};
  --yellow-40: #{$yellow-40};
  --yellow-50: #{$yellow-50};
  --yellow-60: #{$yellow-60};
  --yellow-70: #{$yellow-70};

  /* ====== Semantic CSS color variables ====== */
  --bg-light-primary: var(--white);
  --bg-light-secondary: var(--neutral-10);
  --bg-light-tertiary: var(--neutral-20);
  --bg-light-disabled: var(--neutral-20);
  --bg-dark-primary: var(--neutral-90);
  --bg-dark-secondary: var(--neutral-80);
  --bg-dark-tertiary: var(--neutral-70);
  --bg-dark-disabled: var(--neutral-70);
  --bg-accent-01: var(--green-50);
  --bg-accent-02: var(--green-60);
  --bg-accent-03: var(--green-10);
  --bg-danger-01: var(--red-50);
  --bg-danger-02: var(--red-60);
  --bg-danger-03: var(--red-10);
  --bg-warning-01: var(--yellow-50);
  --bg-warning-02: var(--yellow-60);
  --bg-warning-03: var(--yellow-10);
  --bg-info-01: var(--blue-50);
  --bg-info-02: var(--blue-60);
  --bg-info-03: var(--blue-10);
  --content-primary: var(--neutral-90);
  --content-secondary: var(--neutral-70);
  --content-disabled: var(--neutral-40);
  --content-placeholder: var(--neutral-60);
  --content-danger: var(--red-50);
  --content-warning: var(--yellow-50);
  --content-positive: var(--green-50);
  --content-info: var(--blue-50);
  --border-primary: var(--neutral-60);
  --border-hover: var(--neutral-70);
  --border-disabled: var(--neutral-20);
  --border-active: var(--blue-50);
  --border-danger: var(--red-50);
  --border-divider: var(--neutral-20);
  --border-dark-divider: var(--neutral-70);
  --link-web: var(--green-60);
  --link-web-hover: var(--green-70);
  --link-web-visited: var(--green-60);
  --link-ui: var(--blue-50);
  --link-ui-hover: var(--blue-60);
  --link-ui-visited: var(--blue-60);
  --content-primary-dark: var(--white);
  --content-secondary-dark: var(--neutral-20);
  --content-disabled-dark: var(--neutral-60);
  --content-placeholder-dark: var(--neutral-50);
  --content-danger-dark: var(--red-40);
  --content-warning-dark: var(--yellow-40);
  --content-positive-dark: var(--green-40);
  --content-info-dark: var(--blue-30);
  --border-primary-dark: var(--neutral-30);
  --border-hover-dark: var(--neutral-20);
  --border-disabled-dark: var(--neutral-80);
  --border-active-dark: var(--blue-30);
  --border-danger-dark: var(--red-40);
  --border-divider-dark: var(--neutral-80);
  --link-web-dark: var(--green-30);
  --link-web-hover-dark: var(--green-40);
  --link-web-visited-dark: var(--green-40);
  --link-ui-dark: var(--blue-30);
  --link-ui-hover-dark: var(--blue-40);
  --link-ui-visited-dark: var(--blue-40);

  /* ====== Gradients ====== */
  --premium-gradient: linear-gradient(
    246deg,
    var(--blue-70) 0%,
    #254c84 29%,
    var(--blue-40) 97%
  );
  --premium-gradient-dark: linear-gradient(
    246deg,
    #6f8bcd 0%,
    #8ea3d7 29%,
    #ccdcff 97%
  );
}
